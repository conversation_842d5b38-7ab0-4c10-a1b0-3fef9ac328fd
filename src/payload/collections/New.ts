import type { CollectionConfig } from 'payload'
// import type {
//   TFunction,
// } from '@payloadcms/translations'

// import { CustomTranslationsKeys } from '@payload/custom-translations'

export const New: CollectionConfig = {
  slug: "new",
//   labels: {
//     singular: ({ t: defaultT }) => {
//       const t = defaultT as TFunction<CustomTranslationsKeys>
//       return t('custom:new')
//     },
//     plural: ({ t: defaultT }) => {
//       const t = defaultT as TFunction<CustomTranslationsKeys>
//       return t('custom:new')
//     },
//   },
  admin: {
    useAsTitle: 'name', // 使用虚拟字段 "fullName" 作为标题
    description: '新闻管理',
    group: '文章',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: "name",
      type: "text",
      label: "name",
      localized: true,
      required: true,
    },
    {
        name:"content",
        type:"textarea",
        label:"content",
        localized:true,
        required:false,

    }
  

    

    // ... 其他字段 ...
  ],
  // ... 其他配置 ...
};

